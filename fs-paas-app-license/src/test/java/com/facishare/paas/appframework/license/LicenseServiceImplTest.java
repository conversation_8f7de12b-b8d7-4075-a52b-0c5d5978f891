package com.facishare.paas.appframework.license;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.dto.QuotaInfo;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.license.Result.*;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * LicenseServiceImpl JUnit5 单元测试
 */
@ExtendWith(MockitoExtension.class)
class LicenseServiceImplTest {

  @Mock
  private LicenseClient licenseClient;

  @InjectMocks
  private LicenseServiceImpl licenseService;

  private String tenantId = "12345";
  private User user = User.systemUser(tenantId);

  @BeforeAll
  static void setUpClass() {
    // 初始化测试环境
  }

  @BeforeEach
  void setUp() {
    // 初始化测试环境
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业的所有版本和资源包
   */
  @Test
  @DisplayName("获取企业的所有版本和资源包测试")
  void getVersionAndPackagesTest() {
    // 准备测试数据
    ProductVersionPojo version1 = new ProductVersionPojo();
    version1.setTenantId(tenantId);
    version1.setCurrentVersion("standard_edition");

    ProductVersionPojo version2 = new ProductVersionPojo();
    version2.setTenantId(tenantId);
    version2.setCurrentVersion("extention_package");

    List<ProductVersionPojo> productVersionList = Arrays.asList(version1, version2);
    LicenseVersionResult licenseVersionResult = new LicenseVersionResult();
    licenseVersionResult.setErrCode(PaasMessage.SUCCESS.getCode());
    licenseVersionResult.setResult(productVersionList);

    // 配置Mock行为
    when(licenseClient.queryProductVersion(any())).thenReturn(licenseVersionResult);

    // 执行被测试方法
    List<String> result = licenseService.getVersionAndPackages(tenantId);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.containsAll(Arrays.asList("standard_edition", "extention_package")));

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业的版本
   */
  @Test
  @DisplayName("获取企业的版本测试")
  void getVersionTest() {
    // 准备测试数据
    ProductVersionPojo version1 = new ProductVersionPojo();
    version1.setTenantId(tenantId);
    version1.setProductType("0");
    version1.setCurrentVersion("standard_edition");

    ProductVersionPojo version2 = new ProductVersionPojo();
    version2.setTenantId(tenantId);
    version2.setProductType("1");
    version2.setCurrentVersion("extention_package");

    List<ProductVersionPojo> productVersionList = Arrays.asList(version1, version2);
    LicenseVersionResult licenseVersionResult = new LicenseVersionResult();
    licenseVersionResult.setErrCode(PaasMessage.SUCCESS.getCode());
    licenseVersionResult.setResult(productVersionList);

    // 配置Mock行为
    when(licenseClient.queryProductVersion(any())).thenReturn(licenseVersionResult);

    // 执行被测试方法
    String result = licenseService.getVersion(tenantId);

    // 验证结果
    assertEquals("standard_edition", result);

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业的版本信息
   */
  @Test
  @DisplayName("获取企业的版本信息测试")
  void getVersionInfoTest() {
    // 准备测试数据
    ProductVersionPojo versionPojo = new ProductVersionPojo();
    versionPojo.setTenantId(tenantId);
    versionPojo.setProductType("0");
    versionPojo.setProductName("CRM");
    versionPojo.setCurrentVersion("standard_edition");
    versionPojo.setVersionName("标准版");

    List<ProductVersionPojo> productVersionList = Arrays.asList(versionPojo);
    LicenseVersionResult licenseVersionResult = new LicenseVersionResult();
    licenseVersionResult.setErrCode(PaasMessage.SUCCESS.getCode());
    licenseVersionResult.setResult(productVersionList);

    // 配置Mock行为
    when(licenseClient.queryProductVersion(any())).thenReturn(licenseVersionResult);

    // 执行被测试方法
    GetVersion.VersionInfo result = licenseService.getVersionInfo(tenantId);

    // 验证结果
    assertEquals(tenantId, result.getTenantId());
    assertEquals("0", result.getProductType());
    assertEquals("CRM", result.getProductName());
    assertEquals("standard_edition", result.getCurrentVersion());
    assertEquals("标准版", result.getVersionName());

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业指定模块的数量上限
   */
  @Test
  @DisplayName("获取企业指定模块的数量上限测试")
  void getQuotaByModuleTest() {
    // 准备测试数据
    String paraKey = "custom_objects_limit";
    ModuleParaPojo moduleParaPojo = new ModuleParaPojo();
    moduleParaPojo.setParaValue("100");
    ParaInfoResult paraInfoResult = new ParaInfoResult();
    paraInfoResult.setErrCode(PaasMessage.SUCCESS.getCode());
    paraInfoResult.setResult(Arrays.asList(moduleParaPojo));

    // 配置Mock行为
    when(licenseClient.queryModulePara(any())).thenReturn(paraInfoResult);

    // 执行被测试方法
    int result = licenseService.getQuotaByModule(tenantId, paraKey);

    // 验证结果
    assertEquals(100, result);

    // 验证Mock交互
    verify(licenseClient, times(1)).queryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业指定模块的数量上限，结果为空
   */
  @Test
  @DisplayName("获取企业指定模块的数量上限测试 - 结果为空")
  void getQuotaByModuleEmptyResultTest() {
    // 准备测试数据
    String paraKey = "custom_objects_limit";
    ParaInfoResult paraInfoResult = new ParaInfoResult();
    paraInfoResult.setErrCode(PaasMessage.SUCCESS.getCode());
    paraInfoResult.setResult(Collections.emptyList());

    // 配置Mock行为
    when(licenseClient.queryModulePara(any())).thenReturn(paraInfoResult);

    // 执行被测试方法
    int result = licenseService.getQuotaByModule(tenantId, paraKey);

    // 验证结果
    assertEquals(0, result);

    // 验证Mock交互
    verify(licenseClient, times(1)).queryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业开通的模块
   */
  @Test
  @DisplayName("获取企业开通的模块测试")
  void getModuleTest() {
    // 准备测试数据
    ModuleInfoPojo module1 = new ModuleInfoPojo();
    module1.setModuleCode("custom_object");

    ModuleInfoPojo module2 = new ModuleInfoPojo();
    module2.setModuleCode("custom_roles");

    List<ModuleInfoPojo> moduleInfoList = Arrays.asList(module1, module2);
    ModuleInfoResult moduleInfoResult = new ModuleInfoResult();
    moduleInfoResult.setErrCode(PaasMessage.SUCCESS.getCode());
    moduleInfoResult.setResult(moduleInfoList);

    // 配置Mock行为
    when(licenseClient.queryModule(any())).thenReturn(moduleInfoResult);

    // 执行被测试方法
    Set<String> result = licenseService.getModule(tenantId);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(new ArrayList<>(result).containsAll(Arrays.asList("custom_object", "custom_roles")));

    // 验证Mock交互
    verify(licenseClient, times(1)).queryModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块License
   */
  @Test
  @DisplayName("批量获取模块License测试")
  void batchGetModuleLicensesTest() {
    // 准备测试数据
    User user = User.systemUser(tenantId);
    Map<String, Set<String>> map = Maps.newHashMap();
    map.put("custom_object", Sets.newHashSet("custom_objects_limit", "custom_objects_type_limit"));
    map.put("custom_roles", Sets.newHashSet("custom_roles_limit"));

    ModuleParaPojo pojo1 = new ModuleParaPojo();
    pojo1.setModuleCode("custom_object");
    pojo1.setParaKey("custom_objects_limit");
    pojo1.setParaValue("100");

    ModuleParaPojo pojo2 = new ModuleParaPojo();
    pojo2.setModuleCode("custom_object");
    pojo2.setParaKey("custom_objects_type_limit");
    pojo2.setParaValue("10");

    ModuleParaPojo pojo3 = new ModuleParaPojo();
    pojo3.setModuleCode("custom_roles");
    pojo3.setParaKey("custom_roles_limit");
    pojo3.setParaValue("5");

    List<ModuleParaPojo> moduleParaPojoList1 = Arrays.asList(pojo1, pojo2);
    List<ModuleParaPojo> moduleParaPojoList2 = Arrays.asList(pojo3);

    Map<String, List<ModuleParaPojo>> resultMap = Maps.newHashMap();
    resultMap.put("custom_object", moduleParaPojoList1);
    resultMap.put("custom_roles", moduleParaPojoList2);

    Result<Map<String, List<ModuleParaPojo>>> batchResult = new Result<>();
    batchResult.setErrCode(PaasMessage.SUCCESS.getCode());
    batchResult.setResult(resultMap);

    // 配置Mock行为
    when(licenseClient.batchQueryModulePara(any())).thenReturn(batchResult);

    // 执行被测试方法
    Map<String, List<ModuleParaLicense>> result = licenseService.batchGetModuleLicenses(user, map);

    // 验证结果
    assertEquals(2, result.size());
    assertEquals(2, result.get("custom_object").size());
    assertEquals(1, result.get("custom_roles").size());
    assertEquals("custom_objects_limit", result.get("custom_object").get(0).getParaKey());
    assertEquals("100", result.get("custom_object").get(0).getParaValue());
    assertEquals("custom_roles_limit", result.get("custom_roles").get(0).getParaKey());
    assertEquals("5", result.get("custom_roles").get(0).getParaValue());

    // 验证Mock交互
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块License，结果为空
   */
  @Test
  @DisplayName("批量获取模块License测试 - 结果为空")
  void batchGetModuleLicensesEmptyResultTest() {
    // 准备测试数据
    User user = User.systemUser(tenantId);
    Map<String, Set<String>> map = Maps.newHashMap();
    map.put("custom_object", Sets.newHashSet("custom_objects_limit"));

    Result<Map<String, List<ModuleParaPojo>>> batchResult = new Result<>();
    batchResult.setErrCode(PaasMessage.SUCCESS.getCode());
    batchResult.setResult(null);

    // 配置Mock行为
    when(licenseClient.batchQueryModulePara(any())).thenReturn(batchResult);

    // 执行被测试方法
    Map<String, List<ModuleParaLicense>> result = licenseService.batchGetModuleLicenses(user, map);

    // 验证结果
    assertEquals(1, result.size());
    assertEquals(1, result.get("custom_object").size());
    assertEquals(tenantId, result.get("custom_object").get(0).getTenantId());
    assertEquals("custom_object", result.get("custom_object").get(0).getModuleCode());
    assertEquals("custom_objects_limit", result.get("custom_object").get(0).getParaKey());

    // 验证Mock交互
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试查询可用对象
   */
  @Test
  @DisplayName("查询可用对象测试")
  void queryAvailableObjectTest() {
    // 准备测试数据
    Set<String> apiNames = Sets.newHashSet("Account", "Contact");
    Result<Set<String>> apiNamesResult = new Result<>();
    apiNamesResult.setErrCode(PaasMessage.SUCCESS.getCode());
    apiNamesResult.setResult(apiNames);

    // 配置Mock行为
    when(licenseClient.queryApiNameByLicense(any())).thenReturn(apiNamesResult);

    // 执行被测试方法
    Set<String> result = licenseService.queryAvailableObject(tenantId);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.containsAll(Arrays.asList("Account", "Contact")));

    // 验证Mock交互
    verify(licenseClient, times(1)).queryApiNameByLicense(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取企业支持的预设对象
   */
  @Test
  @DisplayName("获取企业支持的预设对象测试")
  void getObjectApiNamesTest() {
    // 准备测试数据
    String crmKey = "custom_key";
    String moduleType = "custom_type";
    Set<String> apiNames = Sets.newHashSet("Account", "Contact");
    Result<Set<String>> apiNamesResult = new Result<>();
    apiNamesResult.setErrCode(PaasMessage.SUCCESS.getCode());
    apiNamesResult.setResult(apiNames);

    // 配置Mock行为
    when(licenseClient.queryApiNameByLicense(any())).thenReturn(apiNamesResult);

    // 执行被测试方法
    Set<String> result = licenseService.getObjectApiNames(tenantId, crmKey, moduleType);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.containsAll(Arrays.asList("Account", "Contact")));

    // 验证Mock交互
    verify(licenseClient, times(1)).queryApiNameByLicense(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试默认getObjectApiNames方法
   */
  @Test
  @DisplayName("默认getObjectApiNames方法测试")
  void getObjectApiNamesDefaultTest() {
    // 准备测试数据
    Set<String> apiNames = Sets.newHashSet("Account", "Contact");
    Result<Set<String>> apiNamesResult = new Result<>();
    apiNamesResult.setErrCode(PaasMessage.SUCCESS.getCode());
    apiNamesResult.setResult(apiNames);

    // 配置Mock行为
    when(licenseClient.queryApiNameByLicense(any())).thenReturn(apiNamesResult);

    // 执行被测试方法
    Set<String> result = licenseService.getObjectApiNames(tenantId);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.containsAll(Arrays.asList("Account", "Contact")));

    // 验证Mock交互
    verify(licenseClient, times(1)).queryApiNameByLicense(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否开通指定module
   */
  @Test
  @DisplayName("是否开通指定module测试")
  void existModuleTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet("custom_object", "custom_roles");

    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("custom_object").flag(true).build(),
        ModuleFlag.builder().moduleCode("custom_roles").flag(false).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();

    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    Map<String, Boolean> result = licenseService.existModule(tenantId, moduleCodes);

    // 验证结果
    assertEquals(2, result.size());
    assertEquals(true, result.get("custom_object"));
    assertEquals(false, result.get("custom_roles"));

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否开通CRM
   */
  @Test
  @DisplayName("是否开通CRM测试")
  void isOpenCRMTest() {
    // 准备测试数据
    ProductVersionPojo version = new ProductVersionPojo();
    version.setTenantId(tenantId);
    version.setProductType("0");
    version.setCurrentVersion("standard_edition");

    List<ProductVersionPojo> productVersionList = Arrays.asList(version);
    LicenseVersionResult licenseVersionResult = new LicenseVersionResult();
    licenseVersionResult.setErrCode(PaasMessage.SUCCESS.getCode());
    licenseVersionResult.setResult(productVersionList);

    // 配置Mock行为
    when(licenseClient.queryProductVersion(any())).thenReturn(licenseVersionResult);

    // 执行被测试方法
    boolean result = licenseService.isOpenCRM(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持多语言
   */
  @Test
  @DisplayName("是否支持多语言测试")
  void isSupportMultiLanguageTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP);

    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("multi_language_app").flag(true).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportMultiLanguage(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持变更单
   */
  @Test
  @DisplayName("是否支持变更单测试")
  void isSupportChangeOrderTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.OBJECT_CHANGE_ORDER_APP);
    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("object_change_order_app").flag(true).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportChangeOrder(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持推拉单
   */
  @Test
  @DisplayName("是否支持推拉单测试")
  void isSupportConvertRuleTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.PUSH_PULL_ORDER_APP);
    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("push_pull_order_app").flag(true).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportConvertRule(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持OneFlow应用 - 灰度允许且License模块存在
   */
  @Test
  @DisplayName("是否支持OneFlow应用测试 - 灰度允许且License模块存在")
  void isSupportOneFlowAppTest_GrayAllowedAndLicenseExists() {
    try (MockedStatic<UdobjGrayConfig> mockedGrayConfig = mockStatic(UdobjGrayConfig.class)) {
      // 准备测试数据
      Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.ONE_FLOW_APP);
      List<ModuleFlag> moduleFlags = Arrays.asList(
          ModuleFlag.builder().moduleCode("one_flow_app").flag(true).build()
      );
      JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
          .moduleFlags(moduleFlags)
          .build();
      Result<JudgeModulePojo> judgeResult = new Result<>();
      judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
      judgeResult.setResult(judgeModulePojo);

      // 配置Mock行为
      mockedGrayConfig.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ONE_FLOW_GRAY_EI, tenantId))
          .thenReturn(true);
      when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

      // 执行被测试方法
      boolean result = licenseService.isSupportOneFlowApp(tenantId);

      // 验证结果
      assertTrue(result);

      // 验证Mock交互
      verify(licenseClient, times(1)).judgeModule(any());
      mockedGrayConfig.verify(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ONE_FLOW_GRAY_EI, tenantId));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持大对象
   */
  @Test
  @DisplayName("是否支持大对象测试")
  void isSupportBigObjectTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.BIG_OBJECT_APP);
    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("big_object_app").flag(true).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportBigObject(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持多区域
   */
  @Test
  @DisplayName("是否支持多区域测试")
  void isSupportMultiRegionTest() {
    // 准备测试数据
    Set<String> moduleCodes = Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_REGION_APP);
    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("multi_region_app").flag(true).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportMultiRegion(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取已使用配额
   */
  @Test
  @DisplayName("获取已使用配额测试")
  void acquireUsedValueTest() {
    // 准备测试数据
    String licenseKey = "custom_object";
    String paraKey = "custom_objects_limit";
    int count = 5;
    AcquireUsedValuePojo resultObj = new AcquireUsedValuePojo();
    resultObj.setAvailable(true);
    AcquireUsedValueResult acquireResult = new AcquireUsedValueResult();
    acquireResult.setErrCode(PaasMessage.SUCCESS.getCode());
    acquireResult.setResult(resultObj);

    // 配置Mock行为
    when(licenseClient.acquireUsedValue(any())).thenReturn(acquireResult);

    // 执行被测试方法
    boolean result = licenseService.acquireUsedValue(tenantId, licenseKey, paraKey, count);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).acquireUsedValue(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取已使用配额异常情况
   */
  @Test
  @DisplayName("获取已使用配额异常情况测试")
  void acquireUsedValueErrorTest() {
    // 准备测试数据
    String licenseKey = "custom_object";
    String paraKey = "custom_objects_limit";
    int count = 5;
    com.facishare.paas.license.exception.LicenseException licenseException =
        new com.facishare.paas.license.exception.LicenseException("License error");

    // 配置Mock行为 - 抛出异常
    doThrow(licenseException).when(licenseClient).acquireUsedValue(any());

    // 执行并验证异常
    assertThrows(ValidateException.class, () -> {
      licenseService.acquireUsedValue(tenantId, licenseKey, paraKey, count);
    });

    // 验证Mock交互
    verify(licenseClient, times(1)).acquireUsedValue(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取已使用配额
   */
  @Test
  @DisplayName("批量获取已使用配额测试")
  void acquireUsedValueBatchTest() {
    // 准备测试数据
    Set<String> paraKeySet = Sets.newHashSet("custom_objects_limit", "custom_roles_limit");
    OverviewInfoPojo overview1 = new OverviewInfoPojo();
    overview1.setParaKey("custom_objects_limit");
    overview1.setUsedValue("50");

    OverviewInfoPojo overview2 = new OverviewInfoPojo();
    overview2.setParaKey("custom_roles_limit");
    overview2.setUsedValue("10");

    List<OverviewInfoPojo> overviewInfo = Arrays.asList(overview1, overview2);

    OverviewInfoResult overviewInfoResult = new OverviewInfoResult();
    overviewInfoResult.setErrCode(PaasMessage.SUCCESS.getCode());
    overviewInfoResult.setResult(overviewInfo);

    // 配置Mock行为
    when(licenseClient.acquireOverviewUsedValue(any())).thenReturn(overviewInfoResult);

    // 执行被测试方法
    Map<String, String> result = licenseService.acquireUsedValue(tenantId, paraKeySet);

    // 验证结果
    assertEquals(2, result.size());
    assertEquals("50", result.get("custom_objects_limit"));
    assertEquals("10", result.get("custom_roles_limit"));

    // 验证Mock交互
    verify(licenseClient, times(1)).acquireOverviewUsedValue(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试是否支持企业微信标准版
   */
  @Test
  @DisplayName("是否支持企业微信标准版测试")
  void isSupportWechatStandardLicenseTest() {
    // 准备测试数据
    List<ModuleFlag> moduleFlags = Arrays.asList(
        ModuleFlag.builder().moduleCode("wechat_standard_edition").flag(true).build(),
        ModuleFlag.builder().moduleCode("wechat_standard_100_appwechat_standard_50_app").flag(false).build()
    );
    JudgeModulePojo judgeModulePojo = JudgeModulePojo.builder()
        .moduleFlags(moduleFlags)
        .build();
    Result<JudgeModulePojo> judgeResult = new Result<>();
    judgeResult.setErrCode(PaasMessage.SUCCESS.getCode());
    judgeResult.setResult(judgeModulePojo);

    // 配置Mock行为
    when(licenseClient.judgeModule(any())).thenReturn(judgeResult);

    // 执行被测试方法
    boolean result = licenseService.isSupportWechatStandardLicense(tenantId);

    // 验证结果
    assertTrue(result);

    // 验证Mock交互
    verify(licenseClient, times(1)).judgeModule(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取license版本结果为空的情况
   */
  @Test
  @DisplayName("获取license版本结果为空的情况测试")
  void getLicenseVersionErrorTest() {
    // 配置Mock行为 - 返回null
    when(licenseClient.queryProductVersion(any())).thenReturn(null);

    // 执行并验证异常
    assertThrows(APPException.class, () -> {
      licenseService.getVersionAndPackages(tenantId);
    });

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取license版本错误的情况
   */
  @Test
  @DisplayName("获取license版本错误的情况测试")
  void getLicenseVersionFailTest() {
    // 准备测试数据
    LicenseVersionResult licenseVersionResult = new LicenseVersionResult();
    licenseVersionResult.setErrCode(111);
    licenseVersionResult.setErrMessage("Error message");

    // 配置Mock行为
    when(licenseClient.queryProductVersion(any())).thenReturn(licenseVersionResult);

    // 执行并验证异常
    LicenseException exception = assertThrows(LicenseException.class, () -> {
      licenseService.getVersionAndPackages(tenantId);
    });

    // 验证异常信息
    assertEquals("Error message", exception.getMessage());

    // 验证Mock交互
    verify(licenseClient, times(1)).queryProductVersion(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - 正常场景，包含配额和已使用值
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - 正常场景")
  void batchGetQuotaByModuleParaTest_NormalScenario() {
    // 准备测试数据
    User user = User.systemUser(tenantId);
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("custom_object", Sets.newHashSet("custom_objects_limit", "custom_fields_limit"));
    moduleParaMap.put("custom_roles", Sets.newHashSet("custom_roles_limit"));

    // 模拟batchQueryModulePara的返回结果 - 这是batchGetModuleLicenses方法需要的
    List<ModuleParaPojo> customObjectPojos = Arrays.asList(
        createModuleParaPojo("custom_object", "custom_objects_limit", "50"),
        createModuleParaPojo("custom_object", "custom_fields_limit", "200")
    );
    List<ModuleParaPojo> customRolesPojos = Arrays.asList(
        createModuleParaPojo("custom_roles", "custom_roles_limit", "10")
    );

    Map<String, List<ModuleParaPojo>> batchResult = Maps.newHashMap();
    batchResult.put("custom_object", customObjectPojos);
    batchResult.put("custom_roles", customRolesPojos);

    Result<Map<String, List<ModuleParaPojo>>> mockBatchResult = new Result<>();
    mockBatchResult.setErrCode(PaasMessage.SUCCESS.getCode());
    mockBatchResult.setResult(batchResult);

    // 配置Mock行为 - 只需要mock batchQueryModulePara，因为新实现调用batchGetModuleLicenses
    when(licenseClient.batchQueryModulePara(any())).thenReturn(mockBatchResult);

    // 执行被测试方法
    Map<String, Map<String, QuotaInfo>> result = licenseService.batchGetQuotaByModulePara(user, moduleParaMap);

    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());

    // 验证custom_object模块的配额信息
    Map<String, QuotaInfo> customObjectQuotas = result.get("custom_object");
    assertNotNull(customObjectQuotas);
    assertEquals(2, customObjectQuotas.size());

    QuotaInfo objectsLimitQuota = customObjectQuotas.get("custom_objects_limit");
    assertEquals(50, objectsLimitQuota.getTotal());
    assertEquals(0, objectsLimitQuota.getUsed()); // 新实现中从ModuleParaLicense获取，默认为0
    assertEquals(50, objectsLimitQuota.getAvailable());

    QuotaInfo fieldsLimitQuota = customObjectQuotas.get("custom_fields_limit");
    assertEquals(200, fieldsLimitQuota.getTotal());
    assertEquals(0, fieldsLimitQuota.getUsed()); // 新实现中从ModuleParaLicense获取，默认为0
    assertEquals(200, fieldsLimitQuota.getAvailable());

    // 验证custom_roles模块的配额信息
    Map<String, QuotaInfo> customRolesQuotas = result.get("custom_roles");
    assertNotNull(customRolesQuotas);
    assertEquals(1, customRolesQuotas.size());

    QuotaInfo rolesLimitQuota = customRolesQuotas.get("custom_roles_limit");
    assertEquals(10, rolesLimitQuota.getTotal());
    assertEquals(0, rolesLimitQuota.getUsed()); // 新实现中从ModuleParaLicense获取，默认为0
    assertEquals(10, rolesLimitQuota.getAvailable());

    // 验证Mock交互 - 新实现只调用batchQueryModulePara
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - 空模块参数映射场景
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - 空模块参数映射")
  void batchGetQuotaByModuleParaTest_EmptyModuleParaMap() {
    // 准备测试数据
    User user = User.systemUser(tenantId);
    Map<String, Set<String>> emptyModuleParaMap = Maps.newHashMap();

    // 配置Mock行为 - 空映射时返回空结果
    Result<Map<String, List<ModuleParaPojo>>> mockEmptyResult = new Result<>();
    mockEmptyResult.setErrCode(PaasMessage.SUCCESS.getCode());
    mockEmptyResult.setResult(Maps.newHashMap());
    when(licenseClient.batchQueryModulePara(any())).thenReturn(mockEmptyResult);

    // 执行被测试方法
    Map<String, Map<String, QuotaInfo>> result = licenseService.batchGetQuotaByModulePara(user, emptyModuleParaMap);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());

    // 验证Mock交互 - 空映射时会调用batchQueryModulePara但传入空映射
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - null模块参数映射场景
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - null模块参数映射")
  void batchGetQuotaByModuleParaTest_NullModuleParaMap() {
    // 准备测试数据
    User user = User.systemUser(tenantId);

    // 执行并验证异常 - null映射时应该抛出APPException
    APPException exception = assertThrows(APPException.class, () -> {
      licenseService.batchGetQuotaByModulePara(user, null);
    });

    // 验证异常信息
    assertTrue(exception.getMessage().contains("license service error"));

    // 验证Mock交互 - 会调用licenseClient但返回null导致异常
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - license service返回null异常场景
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - license service返回null")
  void batchGetQuotaByModuleParaTest_LicenseServiceReturnsNull() {
    // 准备测试数据
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("test_module", Sets.newHashSet("test_param"));

    // 配置Mock行为 - 返回null
    when(licenseClient.batchQueryModulePara(any())).thenReturn(null);

    // 准备User对象
    User user = User.systemUser(tenantId);

    // 执行并验证异常
    APPException exception = assertThrows(APPException.class, () -> {
      licenseService.batchGetQuotaByModulePara(user, moduleParaMap);
    });

    // 验证异常信息
    assertTrue(exception.getMessage().contains("license service error"));

    // 验证Mock交互
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - license service返回错误码异常场景
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - license service返回错误码")
  void batchGetQuotaByModuleParaTest_ErrorCode() {
    // 准备测试数据
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("test_module", Sets.newHashSet("test_param"));

    // 模拟错误返回结果
    Result<Map<String, List<ModuleParaPojo>>> mockErrorResult = new Result<>();
    mockErrorResult.setErrCode(500);
    mockErrorResult.setErrMessage("Internal server error");

    // 配置Mock行为
    when(licenseClient.batchQueryModulePara(any())).thenReturn(mockErrorResult);

    // 准备User对象
    User user = User.systemUser(tenantId);

    // 执行并验证异常
    LicenseException exception = assertThrows(LicenseException.class, () -> {
      licenseService.batchGetQuotaByModulePara(user, moduleParaMap);
    });

    // 验证异常信息
    assertEquals("Internal server error", exception.getMessage());

    // 验证Mock交互
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - 空结果场景，返回默认配额信息
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - 空结果返回默认配额")
  void batchGetQuotaByModuleParaTest_EmptyResult() {
    // 准备测试数据
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("test_module", Sets.newHashSet("test_param"));

    // 模拟空结果
    Result<Map<String, List<ModuleParaPojo>>> mockEmptyResult = new Result<>();
    mockEmptyResult.setErrCode(PaasMessage.SUCCESS.getCode());
    mockEmptyResult.setResult(null);

    // 配置Mock行为 - 新实现不再调用acquireOverviewUsedValue
    when(licenseClient.batchQueryModulePara(any())).thenReturn(mockEmptyResult);

    // 准备User对象
    User user = User.systemUser(tenantId);

    // 执行被测试方法
    Map<String, Map<String, QuotaInfo>> result = licenseService.batchGetQuotaByModulePara(user, moduleParaMap);

    // 验证结果 - 应该返回默认配额信息
    assertNotNull(result);
    assertEquals(1, result.size());

    Map<String, QuotaInfo> testModuleQuotas = result.get("test_module");
    assertNotNull(testModuleQuotas);
    assertEquals(1, testModuleQuotas.size());

    QuotaInfo testParamQuota = testModuleQuotas.get("test_param");
    assertEquals(0, testParamQuota.getTotal()); // 默认总配额为0
    assertEquals(0, testParamQuota.getUsed());  // 新实现中已使用值也为0（因为没有ModuleParaLicense数据）
    assertEquals(0, testParamQuota.getAvailable()); // 可用配额为0

    // 验证Mock交互 - 新实现只调用batchQueryModulePara
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试批量获取模块参数配额 - 配额值解析异常场景
   */
  @Test
  @DisplayName("批量获取模块参数配额测试 - 配额值解析异常")
  void batchGetQuotaByModuleParaTest_ParseError() {
    // 准备测试数据
    Map<String, Set<String>> moduleParaMap = Maps.newHashMap();
    moduleParaMap.put("test_module", Sets.newHashSet("test_param"));

    // 模拟包含无效配额值的返回结果
    List<ModuleParaPojo> testPojos = Arrays.asList(
        createModuleParaPojo("test_module", "test_param", "invalid_number")
    );

    Map<String, List<ModuleParaPojo>> batchResult = Maps.newHashMap();
    batchResult.put("test_module", testPojos);

    Result<Map<String, List<ModuleParaPojo>>> mockBatchResult = new Result<>();
    mockBatchResult.setErrCode(PaasMessage.SUCCESS.getCode());
    mockBatchResult.setResult(batchResult);

    // 配置Mock行为 - 新实现不再调用acquireOverviewUsedValue
    when(licenseClient.batchQueryModulePara(any())).thenReturn(mockBatchResult);

    // 准备User对象
    User user = User.systemUser(tenantId);

    // 执行被测试方法
    Map<String, Map<String, QuotaInfo>> result = licenseService.batchGetQuotaByModulePara(user, moduleParaMap);

    // 验证结果 - 解析错误时应该返回默认值
    assertNotNull(result);
    assertEquals(1, result.size());

    Map<String, QuotaInfo> testModuleQuotas = result.get("test_module");
    assertNotNull(testModuleQuotas);
    assertEquals(1, testModuleQuotas.size());

    QuotaInfo testParamQuota = testModuleQuotas.get("test_param");
    assertEquals(0, testParamQuota.getTotal()); // 解析错误时默认为0
    assertEquals(0, testParamQuota.getUsed());  // 解析错误时已使用值也为0
    assertEquals(0, testParamQuota.getAvailable()); // 可用配额为0

    // 验证Mock交互 - 新实现只调用batchQueryModulePara
    verify(licenseClient, times(1)).batchQueryModulePara(any());
  }

  // 辅助方法：创建ModuleParaPojo对象
  private ModuleParaPojo createModuleParaPojo(String moduleCode, String paraKey, String paraValue) {
    ModuleParaPojo pojo = new ModuleParaPojo();
    pojo.setModuleCode(moduleCode);
    pojo.setParaKey(paraKey);
    pojo.setParaValue(paraValue);
    return pojo;
  }

  // 辅助方法：创建OverviewInfoPojo对象
  private OverviewInfoPojo createOverviewInfoPojo(String paraKey, String usedValue) {
    OverviewInfoPojo pojo = new OverviewInfoPojo();
    pojo.setParaKey(paraKey);
    pojo.setUsedValue(usedValue);
    return pojo;
  }
}
